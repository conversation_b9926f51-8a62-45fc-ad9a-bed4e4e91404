<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exercise;
use App\Models\Category;
use App\Models\Lesson;
use Illuminate\Http\Request;

class ExerciseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $exercises = Exercise::with(['category', 'lesson', 'user'])
            ->latest()
            ->paginate(15);

        return view('admin.exercises.index', compact('exercises'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)->orderBy('sort_order')->get();
        $lessons = Lesson::where('is_published', true)->orderBy('title_ar')->get();

        return view('admin.exercises.create', compact('categories', 'lessons'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'lesson_id' => 'nullable|exists:lessons,id',
            'title_ar' => 'required|string|max:255',
            'description_ar' => 'required|string',
            'instructions_ar' => 'required|string',
            'type' => 'required|in:quiz,coding,mixed',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'questions' => 'nullable|array',
            'starter_code' => 'nullable|string',
            'solution_code' => 'nullable|string',
            'test_cases' => 'nullable|array',
            'max_attempts' => 'nullable|integer|min:1',
            'time_limit' => 'nullable|integer|min:1',
            'points' => 'required|integer|min:1',
            'is_published' => 'boolean',
        ]);

        $validated['user_id'] = auth()->id();

        Exercise::create($validated);

        return redirect()->route('admin.exercises.index')
            ->with('success', 'تم إنشاء التمرين بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Exercise $exercise)
    {
        $exercise->load(['category', 'lesson', 'user', 'submissions']);
        return view('admin.exercises.show', compact('exercise'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Exercise $exercise)
    {
        $categories = Category::where('is_active', true)->orderBy('sort_order')->get();
        $lessons = Lesson::where('is_published', true)->orderBy('title_ar')->get();

        return view('admin.exercises.edit', compact('exercise', 'categories', 'lessons'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Exercise $exercise)
    {
        $validated = $request->validate([
            'category_id' => 'required|exists:categories,id',
            'lesson_id' => 'nullable|exists:lessons,id',
            'title_ar' => 'required|string|max:255',
            'description_ar' => 'required|string',
            'instructions_ar' => 'required|string',
            'type' => 'required|in:quiz,coding,mixed',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'questions' => 'nullable|array',
            'starter_code' => 'nullable|string',
            'solution_code' => 'nullable|string',
            'test_cases' => 'nullable|array',
            'max_attempts' => 'nullable|integer|min:1',
            'time_limit' => 'nullable|integer|min:1',
            'points' => 'required|integer|min:1',
            'is_published' => 'boolean',
        ]);

        $exercise->update($validated);

        return redirect()->route('admin.exercises.index')
            ->with('success', 'تم تحديث التمرين بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Exercise $exercise)
    {
        $exercise->delete();

        return redirect()->route('admin.exercises.index')
            ->with('success', 'تم حذف التمرين بنجاح');
    }
}
