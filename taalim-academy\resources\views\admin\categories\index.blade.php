@extends('admin.layout')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">إدارة الفئات</h1>
            <p class="text-gray-600">إدارة وتنظيم فئات المحتوى</p>
        </div>
        <a href="{{ route('admin.categories.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
            إضافة فئة جديدة
        </a>
    </div>

    <!-- Categories Grid -->
    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            @if(isset($categories) && $categories->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($categories as $category)
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition duration-150 ease-in-out">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded-full mr-3" style="background-color: {{ $category->color }}"></div>
                                    <h3 class="text-lg font-medium text-gray-900">{{ $category->name_ar }}</h3>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($category->is_active)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            نشط
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            غير نشط
                                        </span>
                                    @endif
                                </div>
                            </div>

                            @if($category->name_en)
                                <p class="text-sm text-gray-600 mb-2">{{ $category->name_en }}</p>
                            @endif

                            @if($category->description_ar)
                                <p class="text-sm text-gray-700 mb-4">{{ Str::limit($category->description_ar, 100) }}</p>
                            @endif

                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span>الترتيب: {{ $category->sort_order }}</span>
                                @if($category->icon)
                                    <i class="{{ $category->icon }} text-lg" style="color: {{ $category->color }}"></i>
                                @endif
                            </div>

                            <div class="flex justify-end space-x-2">
                                <a href="{{ route('admin.categories.show', $category) }}" class="text-indigo-600 hover:text-indigo-900 text-sm">عرض</a>
                                <a href="{{ route('admin.categories.edit', $category) }}" class="text-yellow-600 hover:text-yellow-900 text-sm">تعديل</a>
                                <form action="{{ route('admin.categories.destroy', $category) }}" method="POST" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900 text-sm">حذف</button>
                                </form>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if(method_exists($categories, 'links'))
                    <div class="mt-6">
                        {{ $categories->links() }}
                    </div>
                @endif
            @else
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد فئات</h3>
                    <p class="mt-1 text-sm text-gray-500">ابدأ بإنشاء فئة جديدة لتنظيم المحتوى.</p>
                    <div class="mt-6">
                        <a href="{{ route('admin.categories.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                            إضافة فئة جديدة
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
