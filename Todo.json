{"project": "Arabic Programming Education Platform 'Taalim Academy'", "framework": "<PERSON><PERSON>", "status": "in-progress", "tasks": [{"id": 1, "title": "Initialize Laravel project", "description": "Create a new Laravel app using the latest version", "status": "pending"}, {"id": 2, "title": "Set up authentication", "description": "Install Laravel Breeze or Sanctum for user login/register (student + admin roles)", "status": "pending"}, {"id": 3, "title": "Design database schema", "description": "Create migrations for users, lessons, docs, exercises, categories, and submissions", "status": "pending"}, {"id": 4, "title": "Create admin dashboard", "description": "Build admin views to manage lessons, docs, exercises, and users", "status": "pending"}, {"id": 5, "title": "Build lesson module", "description": "Add support for Markdown lessons, attach code blocks, videos, and images", "status": "pending"}, {"id": 6, "title": "Create documentation module", "description": "Upload and display categorized programming documentation in Arabic", "status": "pending"}, {"id": 7, "title": "Develop interactive exercises", "description": "Create quizzes and programming tasks, allow user submissions and feedback", "status": "pending"}, {"id": 8, "title": "Implement search functionality", "description": "Enable search across lessons and documentation with filters by category or language", "status": "pending"}, {"id": 9, "title": "Create student dashboard", "description": "Allow students to track their progress, saved lessons, and completed exercises", "status": "pending"}, {"id": 10, "title": "Add syntax highlighting", "description": "Integrate Highlight.js or Prism.js to show code with syntax colors", "status": "pending"}, {"id": 11, "title": "Support Arabic UI/UX", "description": "Ensure RTL layout and full Arabic interface and content", "status": "pending"}, {"id": 12, "title": "Deploy application", "description": "Prepare the Laravel app for deployment (configure .env, hosting, database)", "status": "pending"}]}